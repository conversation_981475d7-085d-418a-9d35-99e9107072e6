{"name": "RT-rWMS", "version": "3.8.3", "description": "RT-rWMS", "author": "成都瑞特数字科技有限责任公司", "license": "Apache-2.0", "type": "module", "scripts": {"dev": "vite --force", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/dist/", "lint:eslint": "eslint --fix --ext .js,.cjs,.mjs,.ts,.vue ./src", "prettier": "prettier --write ."}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "@types/prismjs": "^1.26.5", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "autoprefixer": "10.4.20", "axios": "1.8.2", "china-area-data": "^5.0.1", "codemirror": "5.65.18", "crypto-js": "4.2.0", "driver.js": "1.3.1", "echarts": "5.5.1", "element-plus": "2.8.7", "js-cookie": "3.0.5", "markdown-it": "^14.1.0", "mitt": "3.0.1", "nprogress": "0.2.0", "pinia": "2.3.0", "postcss": "8.4.49", "prismjs": "^1.30.0", "qiankun": "^2.10.16", "qs": "6.13.1", "screenfull": "6.0.2", "sm-crypto": "0.3.13", "sortablejs": "1.15.6", "splitpanes": "3.1.8", "tailwindcss": "3.4.17", "vue": "3.5.13", "vue-clipboard3": "2.0.0", "vue-echarts": "7.0.3", "vue-i18n": "11.1.2", "vue-router": "4.4.5"}, "devDependencies": {"@swc/core": "1.6.13", "@types/markdown-it": "^14.1.2", "@types/node": "20.0.0", "@types/nprogress": "0.2.3", "@types/sortablejs": "1.15.8", "@typescript-eslint/eslint-plugin": "8.17.0", "@typescript-eslint/parser": "8.17.0", "@vitejs/plugin-vue": "5.2.1", "@vue/compiler-sfc": "3.5.13", "consola": "3.2.3", "cross-env": "7.0.3", "eslint": "9.14.0", "eslint-plugin-vue": "9.32.0", "glob": "9.3.5", "pinia-plugin-persist": "1.0.0", "prettier": "3.4.2", "sass": "1.58.3", "terser": "5.36.0", "typescript": "5.6.3", "unplugin-auto-import": "0.18.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.4", "vite-plugin-compression": "0.5.1", "vite-plugin-top-level-await": "1.4.4", "vite-plugin-vue-devtools": "7.7.2", "vue-eslint-parser": "9.4.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": ""}, "engines": {"node": ">=18.0.0", "npm": ">= 8.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"], "repository": {"type": "git", "url": "http://*************:8085/"}}