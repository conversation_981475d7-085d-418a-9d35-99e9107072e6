import { defineStore } from 'pinia';

/**
 * 开发者工具检测状态管理
 */
export interface DevToolsDetectionState {
	// 是否检测到开发者工具
	isDevToolsOpen: boolean;
	// 是否显示警告页面
	showWarning: boolean;
	// 检测是否启用
	detectionEnabled: boolean;
	// 是否已初始化
	initialized: boolean;
}

export const useDevToolsDetectionStore = defineStore('devToolsDetection', {
	state: (): DevToolsDetectionState => ({
		isDevToolsOpen: false,
		showWarning: false,
		detectionEnabled: true,
		initialized: false,
	}),

	// 确保此 store 不被持久化，避免状态缓存问题
	persist: {
		enabled: false,
	},

	getters: {
		/**
		 * 是否应该阻止API请求
		 */
		shouldBlockRequests: (state): boolean => {
			return state.detectionEnabled && state.isDevToolsOpen;
		},

		/**
		 * 是否应该显示警告页面
		 */
		shouldShowWarning: (state): boolean => {
			return state.detectionEnabled && state.showWarning;
		},
	},

	actions: {
		/**
		 * 设置开发者工具检测状态
		 */
		setDevToolsOpen(isOpen: boolean) {
			this.isDevToolsOpen = isOpen;
			this.showWarning = isOpen;
		},

		/**
		 * 设置检测是否启用
		 */
		setDetectionEnabled(enabled: boolean) {
			this.detectionEnabled = enabled;
			if (!enabled) {
				this.isDevToolsOpen = false;
				this.showWarning = false;
			}
		},

		/**
		 * 隐藏警告页面
		 */
		hideWarning() {
			this.showWarning = false;
		},

		/**
		 * 显示警告页面
		 */
		showWarningPage() {
			if (this.detectionEnabled && this.isDevToolsOpen) {
				this.showWarning = true;
			}
		},

		/**
		 * 重置状态
		 */
		reset() {
			this.isDevToolsOpen = false;
			this.showWarning = false;
			this.initialized = false;
		},

		/**
		 * 标记为已初始化
		 */
		setInitialized() {
			this.initialized = true;
		},
	},
});
