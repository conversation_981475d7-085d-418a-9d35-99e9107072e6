import { registerMicroApps, start, setDefaultMountApp } from 'qiankun';
import { Session } from '/@/utils/storage';

/**
 * 微前端应用配置
 */
const microApps = [
  {
    name: 'wms-app',
    entry: process.env.NODE_ENV === 'development' ? '//localhost:3001' : '/wms/',
    container: '#micro-container',
    activeRule: '/wms',
    props: {
      routerBase: '/wms',
      getGlobalState: () => ({
        token: Session.getToken(),
        userInfo: Session.get('userInfo'),
      }),
    },
  },
  {
    name: 'erp-app', 
    entry: process.env.NODE_ENV === 'development' ? '//localhost:3002' : '/erp/',
    container: '#micro-container',
    activeRule: '/erp',
    props: {
      routerBase: '/erp',
      getGlobalState: () => ({
        token: Session.getToken(),
        userInfo: Session.get('userInfo'),
      }),
    },
  },
];

/**
 * 注册微前端应用
 */
export function registerApps() {
  registerMicroApps(microApps, {
    beforeLoad: [
      (app) => {
        console.log('[LifeCycle] before load %c%s', 'color: green;', app.name);
        return Promise.resolve();
      },
    ],
    beforeMount: [
      (app) => {
        console.log('[LifeCycle] before mount %c%s', 'color: green;', app.name);
        return Promise.resolve();
      },
    ],
    afterUnmount: [
      (app) => {
        console.log('[LifeCycle] after unmount %c%s', 'color: green;', app.name);
        return Promise.resolve();
      },
    ],
  });
}

/**
 * 启动微前端
 */
export function startMicroApp() {
  start({
    prefetch: false, // 预加载
    sandbox: {
      experimentalStyleIsolation: true, // 样式隔离
    },
    singular: false, // 是否为单实例场景
  });
}

/**
 * 设置默认应用
 */
export function setDefaultApp() {
  setDefaultMountApp('/home');
}
