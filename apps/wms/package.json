{"name": "wms-app", "version": "1.0.0", "description": "WMS微前端子应用", "type": "module", "scripts": {"dev": "vite --port 3001", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.8.2", "element-plus": "2.8.7", "vue": "3.5.13", "vue-router": "4.4.5", "pinia": "2.3.0", "vue-i18n": "11.1.2"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.1", "@vue/compiler-sfc": "3.5.13", "typescript": "5.6.3", "vite": "6.3.4", "vite-plugin-qiankun": "^1.0.17"}}